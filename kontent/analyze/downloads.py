"""
Need run this file in application startup to loads libraries and corpus to NLTK.
"""
import nltk

# JUST for AWS Beanstalk
DIR_NLTK = '/app/nltk_data'

# Configure NLTK data path to include our custom directory
nltk.data.path.append(DIR_NLTK)

nltk.download('averaged_perceptron_tagger', download_dir=DIR_NLTK)
nltk.download('floresta', download_dir=DIR_NLTK)
nltk.download('mac_morpho', download_dir=DIR_NLTK)
nltk.download('machado', download_dir=DIR_NLTK)
nltk.download('punkt', download_dir=DIR_NLTK)
nltk.download('punkt_tab', download_dir=DIR_NLTK)
nltk.download('stopwords', download_dir=DIR_NLTK)
nltk.download('wordnet', download_dir=DIR_NLTK)
nltk.download('words', download_dir=DIR_NLTK)
