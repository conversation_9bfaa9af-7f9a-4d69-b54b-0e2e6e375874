import warnings
# Suppress pkg_resources deprecation warning from breadability (sumy dependency)
warnings.filterwarnings("ignore", message="pkg_resources is deprecated", category=UserWarning)

from sumy.nlp.tokenizers import Tokenizer
from sumy.parsers.html import HtmlParser


def url2text(url):
    parser = HtmlParser.from_url(url, Tokenizer('english'))

    text = ''

    for paragraph in parser.document.paragraphs:
        if paragraph.headings:
            for header in paragraph.headings:  # pylint: disable=protected-access
                text += '\n\n{}\n'.format(header._text)  # pylint: disable=protected-access

        if paragraph.sentences:
            _sentence = ''
            for sentence in paragraph.sentences:  # pylint: disable=protected-access
                _sentence += ' {}'.format(sentence._text)  # pylint: disable=protected-access

            text += "\n{}".format(_sentence)

    return text
