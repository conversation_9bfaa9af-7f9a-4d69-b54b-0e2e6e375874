# -*- coding: utf-8 -*-
from __future__ import absolute_import
from __future__ import division, print_function, unicode_literals

from sumy.parsers.html import HtmlParser
from sumy.parsers.plaintext import PlaintextParser
from sumy.nlp.tokenizers import Tokenizer
from sumy.summarizers.lsa import LsaSummarizer as Summarizer
from sumy.nlp.stemmers import Stemmer
from sumy.utils import get_stop_words


class Summarize:
    """
    Method to get a text or URL and extract a summary
    """
    def __init__(self) -> None:
        pass

    @staticmethod
    def text(text: str, language: str, sentence_size: int) -> str:
        """
        text summarize

        :param text: The string content
        :param language: text language
        :param sentence_size: size of summary in number of sentences

        :return: summary

        """
        print(text)
        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        parser = PlaintextParser.from_string(text, Tokenizer(language))
        stemmer = Stemmer(language)

        summarizer = Summarizer(stemmer)
        summarizer.stop_words = get_stop_words(language)

        sentences = []

        for sentence in summarizer(parser.document, sentence_size):
            sentences.append(sentence._text)  # pylint: disable=protected-access

        return '\n'.join(sentences)

    @staticmethod
    def url(url: str, language: str, sentence_size: int) -> str:
        """
        extract text content from URL to summarize

        :param url: The URL from blog, post, ...
        :param language: text language
        :param sentence_size: size of summary in number of sentences

        :return: summary

        """
        parser = HtmlParser.from_url(url, Tokenizer(language))
        stemmer = Stemmer(language)

        summarizer = Summarizer(stemmer)
        summarizer.stop_words = get_stop_words(language)

        sentences = []

        for sentence in summarizer(parser.document, sentence_size):
            sentences.append(sentence._text)  # pylint: disable=protected-access

        return '\n'.join(sentences)
