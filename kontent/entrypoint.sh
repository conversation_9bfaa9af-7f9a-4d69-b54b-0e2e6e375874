#!/bin/sh

if [ "$DATABASE" = "postgres" ]
then
    echo "Waiting for postgres..."

    while ! nc -z $DATABASE_HOST $DATABASE_PORT; do
      sleep 0.1
    done

    echo "PostgreSQL started"
fi


# Function to create necessary directories
create_directories() {
    mkdir -p /app/static /app/media /app/temp
    log "Created necessary directories"
}
create_directories

# Main exec

python manage.py migrate
python manage.py collectstatic --noinput

exec "$@"
