# Use an official Python runtime as a parent image
FROM python:3.12-slim-bookworm

# Set environment variables for Python to avoid writing .pyc files and buffer stdout/stderr
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH=/app/.local/bin/:$PATH \
    NLTK_DATA=/app/nltk_data

# Set the working directory
WORKDIR /app

# Expose the port your app will run on
EXPOSE 8000

# Use arguments for variables that might change
ARG arg_revision
ARG arg_build_date

# Assign arguments to environment variables if they are provided
ENV REVISION=${arg_revision:-$REVISION} \
    BUILD_DATE=${arg_build_date:-$BUILD_DATE}

# Update the package index and install only the necessary dependencies,
# then remove packages post-installation to keep the image lean
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    ffmpeg \
    g++ \
    gcc \
    libpq-dev \
    supervisor \
    swig \
    && rm -rf /var/lib/apt/lists/*


# Copy only what's necessary to install dependencies first
# This better leverages <PERSON><PERSON>'s layer caching
COPY requirements.txt /app/

# Install Python dependencies
RUN pip install pip setuptools && pip install gunicorn && \
    pip install -r requirements.txt && pip install python-dateutil

# Copy the rest of the application code
COPY . /app
COPY supervisord.conf /etc/supervisord.conf

# Download NLTK packages and Adjust file permissions
RUN python analyze/downloads.py && chmod +x /app/entrypoint.sh

# Define the entry point
ENTRYPOINT ["/app/entrypoint.sh"]
